import React, { useState } from 'react';
import { aiInterviewEvaluationsApi, promptsApi } from '../services/api';

// Annotation selector component
const AnnotationSelector = ({ evaluationId, currentAnnotation, onAnnotationUpdate }) => {
  const [updating, setUpdating] = useState(false);

  const handleAnnotationChange = async (annotation) => {
    try {
      setUpdating(true);
      const response = await aiInterviewEvaluationsApi.updateAnnotation(evaluationId, annotation);
      onAnnotationUpdate(response.data);
    } catch (error) {
      console.error('Error updating annotation:', error);
      alert('Failed to update annotation');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
      <button
        onClick={() => handleAnnotationChange('good')}
        disabled={updating}
        style={{
          padding: '4px 8px',
          border: currentAnnotation === 'good' ? '2px solid #28a745' : '1px solid #28a745',
          borderRadius: '4px',
          backgroundColor: currentAnnotation === 'good' ? '#28a745' : 'white',
          color: currentAnnotation === 'good' ? 'white' : '#28a745',
          cursor: updating ? 'not-allowed' : 'pointer',
          fontSize: '12px',
          fontWeight: currentAnnotation === 'good' ? 'bold' : 'normal'
        }}
      >
        👍 Good
      </button>
      <button
        onClick={() => handleAnnotationChange('not good')}
        disabled={updating}
        style={{
          padding: '4px 8px',
          border: currentAnnotation === 'not good' ? '2px solid #dc3545' : '1px solid #dc3545',
          borderRadius: '4px',
          backgroundColor: currentAnnotation === 'not good' ? '#dc3545' : 'white',
          color: currentAnnotation === 'not good' ? 'white' : '#dc3545',
          cursor: updating ? 'not-allowed' : 'pointer',
          fontSize: '12px',
          fontWeight: currentAnnotation === 'not good' ? 'bold' : 'normal'
        }}
      >
        👎 Not Good
      </button>
    </div>
  );
};

// Modal component for displaying prompt content
const PromptModal = ({ isOpen, onClose, promptData, promptName, loading }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80%',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ margin: 0 }}>{promptName}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              padding: '0',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        
        {loading ? (
          <div>Loading prompt content...</div>
        ) : (
          <div style={{
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            fontSize: '14px',
            backgroundColor: '#f8f9fa',
            padding: '16px',
            borderRadius: '4px',
            border: '1px solid #ddd'
          }}>
            {promptData}
          </div>
        )}
      </div>
    </div>
  );
};

const AIInterviewResultsTable = ({ evaluations, onEvaluationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const handleAnnotationUpdate = (updatedEvaluation) => {
    onEvaluationUpdate(updatedEvaluation);
  };

  const handlePromptVersionClick = async (promptId, version, promptName, evaluation) => {
    setModalState({
      isOpen: true,
      promptData: null,
      promptName: `${promptName} (Version ${version})`,
      loading: true
    });

    try {
      const response = await promptsApi.getVersion(promptId, version);
      setModalState(prev => ({
        ...prev,
        promptData: response.data.content,
        loading: false
      }));
    } catch (error) {
      console.error('Error fetching prompt version:', error);
      
      // Fallback to stored content in evaluation
      let fallbackContent = '';
      if (promptId === 5) fallbackContent = evaluation.prompt1Content;
      else if (promptId === 6) fallbackContent = evaluation.prompt2Content;
      else if (promptId === 7) fallbackContent = evaluation.prompt3Content;
      
      setModalState(prev => ({
        ...prev,
        promptData: fallbackContent || 'Error loading prompt content',
        loading: false
      }));
    }
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case 'in_progress':
        return { text: 'Processing...', color: '#ffc107', bgColor: '#fff3cd' };
      case 'completed':
        return { text: 'Completed', color: '#28a745', bgColor: '#d4edda' };
      case 'error':
        return { text: 'Error', color: '#dc3545', bgColor: '#f8d7da' };
      default:
        return { text: status, color: '#6c757d', bgColor: '#e9ecef' };
    }
  };

  const formatOutput = (output) => {
    if (!output) return 'Processing...';
    
    if (typeof output === 'string') {
      return output;
    }

    if (typeof output === 'object') {
      // Try to format the insight summary if available
      if (output.insight) {
        return output.insight;
      }
      
      // Otherwise show a summary of evaluations
      if (output.evaluations) {
        const criteriaCount = Object.keys(output.evaluations).filter(key => 
          key !== 'list_analysis' && key !== 'summary'
        ).length;
        return `Evaluation completed for ${criteriaCount} criteria.`;
      }
      
      return JSON.stringify(output, null, 2);
    }

    return String(output);
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: 'white'
    }}>
      <h3 style={{ marginTop: 0 }}>AI Interview Evaluation Results</h3>

      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
        loading={modalState.loading}
      />

      <div style={{ overflow: 'auto' }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '14px'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Output
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div>
                      P1: <button
                        onClick={() => handlePromptVersionClick(5, evaluation.prompt1Version, 'Question Evaluator', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt1Version}
                      </button>
                    </div>
                    <div>
                      P2: <button
                        onClick={() => handlePromptVersionClick(6, evaluation.prompt2Version, 'Summary Evaluator', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt2Version}
                      </button>
                    </div>
                    <div>
                      P3: <button
                        onClick={() => handlePromptVersionClick(7, evaluation.prompt3Version, 'Insight Summary', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt3Version}
                      </button>
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.dataset_name}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: getStatusDisplay(evaluation.status).color,
                      backgroundColor: getStatusDisplay(evaluation.status).bgColor
                    }}>
                      {getStatusDisplay(evaluation.status).text}
                    </span>
                  </td>
                  <td style={{
                    padding: '12px',
                    verticalAlign: 'top',
                    maxWidth: '300px'
                  }}>
                    <div style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {formatOutput(evaluation.output)}
                    </div>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={handleAnnotationUpdate}
                    />
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '4px 8px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide Details' : 'View Details'}
                    </button>
                  </td>
                </tr>

                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="7" style={{
                      padding: '16px',
                      backgroundColor: '#f8f9fa',
                      borderBottom: '1px solid #ddd'
                    }}>
                      <div style={{ display: 'grid', gap: '16px' }}>
                        <div>
                          <h4 style={{ margin: '0 0 8px 0' }}>Final Output:</h4>
                          <div style={{
                            padding: '12px',
                            backgroundColor: 'white',
                            border: '1px solid #ddd',
                            borderRadius: '4px',
                            fontSize: '13px',
                            fontFamily: 'monospace',
                            whiteSpace: 'pre-wrap',
                            maxHeight: '400px',
                            overflow: 'auto'
                          }}>
                            {evaluation.output ? JSON.stringify(evaluation.output, null, 2) : 'Processing...'}
                          </div>
                        </div>

                        {evaluation.details && (
                          <div>
                            <h4 style={{ margin: '0 0 8px 0' }}>Processing Details:</h4>
                            <div style={{
                              padding: '12px',
                              backgroundColor: 'white',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              fontSize: '13px',
                              fontFamily: 'monospace',
                              whiteSpace: 'pre-wrap',
                              maxHeight: '400px',
                              overflow: 'auto'
                            }}>
                              {JSON.stringify(evaluation.details, null, 2)}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>

        {evaluations.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#666',
            fontStyle: 'italic'
          }}>
            No AI Interview evaluations found. Run an evaluation to see results here.
          </div>
        )}
      </div>
    </div>
  );
};

export default AIInterviewResultsTable;
