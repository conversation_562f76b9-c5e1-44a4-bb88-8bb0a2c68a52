class AIInterviewGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(prompt, responseFormat = "application/json") {
    try {
      await this.initPromise;
      const response = await this.client.models.generateContent({
        model: 'gemini-2.5-flash-preview-05-20',
        contents: prompt,
        config: {
          temperature: 0.2,
          responseMimeType: responseFormat
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw error;
    }
  }

  async runQuestionEvaluator(questionAnswerPair, prompt1Content) {
    const userPrompt = `Question: ${questionAnswerPair.question}
Answer: ${questionAnswerPair.answer}`;

    const prompt = [
      {
        role: 'user',
        parts: [{ text: userPrompt }]
      }
    ];

    // Add system instruction as first message
    prompt.unshift({
      role: 'user',
      parts: [{ text: prompt1Content }]
    });

    const response = await this.generateResponse(prompt);
    
    try {
      const parsed = JSON.parse(response);
      return {
        questionAnswerPair,
        evaluation: parsed,
        rawResponse: response
      };
    } catch (error) {
      console.error('Error parsing question evaluator response:', error);
      throw new Error('Failed to parse question evaluator response');
    }
  }

  async runSummaryEvaluator(questionEvaluations, prompt2Content) {
    // Aggregate all evaluations
    const aggregatedData = {
      evaluations: {},
      video_summaries: []
    };

    // Process each question evaluation
    questionEvaluations.forEach((qe, index) => {
      if (qe.evaluation && qe.evaluation.evaluations) {
        Object.keys(qe.evaluation.evaluations).forEach(criteria => {
          if (!aggregatedData.evaluations[criteria]) {
            aggregatedData.evaluations[criteria] = {
              analysis: [],
              scores: []
            };
          }
          
          const evaluation = qe.evaluation.evaluations[criteria];
          if (evaluation.analysis) {
            aggregatedData.evaluations[criteria].analysis.push(evaluation.analysis);
          }
          if (evaluation.score !== null && evaluation.score !== undefined) {
            aggregatedData.evaluations[criteria].scores.push(evaluation.score);
          }
        });
      }

      // Add transcript as video summary
      if (qe.evaluation && qe.evaluation.transcript) {
        aggregatedData.video_summaries.push(qe.evaluation.transcript);
      }
    });

    // Calculate average scores
    Object.keys(aggregatedData.evaluations).forEach(criteria => {
      const scores = aggregatedData.evaluations[criteria].scores;
      if (scores.length > 0) {
        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        aggregatedData.evaluations[criteria].score = average;
      }
    });

    const userPrompt = `Here is the data of the interview:
${JSON.stringify(aggregatedData)}`;

    const prompt = [
      {
        role: 'user',
        parts: [{ text: userPrompt }]
      }
    ];

    // Add system instruction as first message
    prompt.unshift({
      role: 'user',
      parts: [{ text: prompt2Content }]
    });

    const response = await this.generateResponse(prompt);
    
    try {
      const parsed = JSON.parse(response);
      return {
        summaryEvaluation: parsed,
        aggregatedData,
        rawResponse: response
      };
    } catch (error) {
      console.error('Error parsing summary evaluator response:', error);
      throw new Error('Failed to parse summary evaluator response');
    }
  }

  async runInsightSummary(summaryEvaluation, prompt3Content) {
    // Transform summary evaluation to the expected format for insight summary
    const evaluationData = {};
    
    Object.keys(summaryEvaluation).forEach(criteria => {
      if (criteria !== 'list_analysis' && criteria !== 'summary') {
        evaluationData[criteria] = {
          scores: 0, // We'll need to calculate this from the original data
          analysis: summaryEvaluation[criteria]
        };
      }
    });

    const userPrompt = `Here the candidate summary data
${JSON.stringify(evaluationData)}`;

    const prompt = [
      {
        role: 'user',
        parts: [{ text: userPrompt }]
      }
    ];

    // Add system instruction as first message
    prompt.unshift({
      role: 'user',
      parts: [{ text: prompt3Content }]
    });

    const response = await this.generateResponse(prompt, "text/plain");
    
    return {
      insightSummary: response,
      rawResponse: response
    };
  }

  async runAIInterviewPromptChain(dataset, prompt1Content, prompt2Content, prompt3Content) {
    try {
      console.log('Starting AI Interview evaluation chain...');
      
      // Step 1: Run question evaluator for each question-answer pair
      console.log('Step 1: Running question evaluators...');
      const questionEvaluations = [];
      
      for (const questionAnswerPair of dataset.data) {
        try {
          const evaluation = await this.runQuestionEvaluator(questionAnswerPair, prompt1Content);
          questionEvaluations.push(evaluation);
          console.log(`Evaluated question: ${questionAnswerPair.question.substring(0, 50)}...`);
        } catch (error) {
          console.error('Error evaluating question:', error);
          // Continue with other questions even if one fails
          questionEvaluations.push({
            questionAnswerPair,
            evaluation: null,
            error: error.message
          });
        }
      }

      // Step 2: Run summary evaluator
      console.log('Step 2: Running summary evaluator...');
      const summaryResult = await this.runSummaryEvaluator(questionEvaluations, prompt2Content);

      // Step 3: Run insight summary
      console.log('Step 3: Running insight summary...');
      const insightResult = await this.runInsightSummary(summaryResult.summaryEvaluation, prompt3Content);

      // Combine all results
      const finalResult = {
        questionEvaluations,
        summaryEvaluation: summaryResult.summaryEvaluation,
        aggregatedData: summaryResult.aggregatedData,
        insightSummary: insightResult.insightSummary,
        finalOutput: {
          evaluations: summaryResult.summaryEvaluation,
          insight: insightResult.insightSummary,
          details: questionEvaluations
        }
      };

      console.log('AI Interview evaluation chain completed successfully');
      return finalResult;

    } catch (error) {
      console.error('Error in AI Interview prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new AIInterviewGeminiService();
