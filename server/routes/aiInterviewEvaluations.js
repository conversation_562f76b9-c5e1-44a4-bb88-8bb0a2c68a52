const express = require('express');
const router = express.Router();
const aiInterviewGeminiService = require('../services/aiInterviewGemini');
const supabase = require('../supabaseClient');

// Background processing function
async function processAIInterviewEvaluationAsync(evaluationId, dataset, prompt1Content, prompt2Content, prompt3Content) {
  try {
    console.log(`Starting background processing for AI Interview evaluation ${evaluationId}`);

    // Run the AI Interview prompt chain
    const result = await aiInterviewGeminiService.runAIInterviewPromptChain(
      dataset, 
      prompt1Content, 
      prompt2Content, 
      prompt3Content
    );

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('ai_interview_evaluations')
      .update({
        output: result.finalOutput,
        status: 'completed',
        details: result
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating AI Interview evaluation:', updateError);
      
      // Update status to error
      await supabase
        .from('ai_interview_evaluations')
        .update({ status: 'error' })
        .eq('id', evaluationId);
    } else {
      console.log(`AI Interview evaluation ${evaluationId} completed successfully`);
    }

  } catch (error) {
    console.error(`Error processing AI Interview evaluation ${evaluationId}:`, error);
    
    // Update status to error
    await supabase
      .from('ai_interview_evaluations')
      .update({ status: 'error' })
      .eq('id', evaluationId);
  }
}

// GET all AI Interview evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('ai_interview_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching AI Interview evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch AI Interview evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /ai-interview-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch AI Interview evaluations' });
  }
});

// GET AI Interview evaluation status by ID
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data, error } = await supabase
      .from('ai_interview_evaluations')
      .select('id, status, output, details')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching AI Interview evaluation status:', error);
      return res.status(500).json({ error: 'Failed to fetch evaluation status' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /ai-interview-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Failed to fetch evaluation status' });
  }
});

// PUT update annotation for AI Interview evaluation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    if (!annotation || !['good', 'not good'].includes(annotation)) {
      return res.status(400).json({ error: 'Annotation must be either "good" or "not good"' });
    }

    const { data, error } = await supabase
      .from('ai_interview_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating AI Interview evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /ai-interview-evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// POST run new AI Interview evaluation
router.post('/run', async (req, res) => {
  try {
    const { datasetId } = req.body;

    if (!datasetId) {
      return res.status(400).json({ error: 'Dataset ID is required' });
    }

    // Fetch the dataset
    const { data: dataset, error: datasetError } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', datasetId)
      .single();

    if (datasetError || !dataset) {
      console.error('Error fetching dataset:', datasetError);
      return res.status(400).json({ error: 'Dataset not found' });
    }

    // Validate dataset structure
    if (!dataset.data || !Array.isArray(dataset.data.data)) {
      return res.status(400).json({ error: 'Invalid dataset structure' });
    }

    // Fetch AI Interview prompts (IDs 5, 6, 7)
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [5, 6, 7]);

    if (promptsError) {
      console.error('Error fetching prompts:', promptsError);
      return res.status(500).json({ error: 'Failed to fetch prompts' });
    }

    const prompt1 = prompts.find(p => p.id === 5); // Question Evaluator
    const prompt2 = prompts.find(p => p.id === 6); // Summary Evaluator
    const prompt3 = prompts.find(p => p.id === 7); // Insight Summary

    if (!prompt1 || !prompt2 || !prompt3) {
      return res.status(400).json({ error: 'Required AI Interview prompts not found in Supabase' });
    }

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      dataset_id: datasetId,
      dataset_name: dataset.name,
      output: null,
      status: 'in_progress',
      prompt1Version: prompt1.version,
      prompt2Version: prompt2.version,
      prompt3Version: prompt3.version,
      prompt1Content: prompt1.content,
      prompt2Content: prompt2.content,
      prompt3Content: prompt3.content,
      timestamp: new Date().toISOString(),
      details: null
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('ai_interview_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting AI Interview evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save AI Interview evaluation' });
    }

    // Start background processing
    processAIInterviewEvaluationAsync(
      newEvaluation.id, 
      dataset, 
      prompt1.content, 
      prompt2.content, 
      prompt3.content
    );

    // Return the evaluation record immediately
    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /ai-interview-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run AI Interview evaluation' });
  }
});

module.exports = router;
